-- ========================================
-- NTA 3.0 告警管理模块数据库结构
-- ========================================
-- 创建时间: 2025-01-22
-- 描述: 告警管理模块相关的所有表结构定义
-- 数据库: PostgreSQL
-- ========================================

-- ========================================
-- 告警核心业务表
-- ========================================

-- 告警主表（对应Alarm实体）
DROP TABLE IF EXISTS alarm CASCADE;

CREATE TABLE alarm (
    id VARCHAR(32) PRIMARY KEY,
    alarm_type VARCHAR(100),
    alarm_level VARCHAR(20),
    alarm_time TIMESTAMP,
    src_ip VARCHAR(45),
    dst_ip VARCHAR(45),
    src_port INTEGER,
    dst_port INTEGER,
    protocol VARCHAR(20),
    description TEXT,
    status VARCHAR(20) DEFAULT 'OPEN',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    task_id INTEGER,
    session_id VARCHAR(64),
    rule_id INTEGER,
    confidence DECIMAL(5,2),
    severity INTEGER,
    category VARCHAR(50),
    sub_category VARCHAR(50),
    attack_stage VARCHAR(50),
    threat_intelligence TEXT,
    remediation_advice TEXT,
    false_positive BOOLEAN DEFAULT FALSE,
    analyst_notes TEXT,
    escalated BOOLEAN DEFAULT FALSE,
    escalation_time TIMESTAMP,
    resolved_time TIMESTAMP,
    resolved_by VARCHAR(100),
    resolution_notes TEXT
);

COMMENT ON TABLE alarm IS '告警主表';
COMMENT ON COLUMN alarm.id IS '告警唯一标识';
COMMENT ON COLUMN alarm.alarm_type IS '告警类型';
COMMENT ON COLUMN alarm.alarm_level IS '告警级别';
COMMENT ON COLUMN alarm.alarm_time IS '告警时间';
COMMENT ON COLUMN alarm.src_ip IS '源IP地址';
COMMENT ON COLUMN alarm.dst_ip IS '目标IP地址';
COMMENT ON COLUMN alarm.src_port IS '源端口';
COMMENT ON COLUMN alarm.dst_port IS '目标端口';
COMMENT ON COLUMN alarm.protocol IS '协议类型';
COMMENT ON COLUMN alarm.description IS '告警描述';
COMMENT ON COLUMN alarm.status IS '告警状态：OPEN-开放，INVESTIGATING-调查中，RESOLVED-已解决，CLOSED-已关闭';
COMMENT ON COLUMN alarm.task_id IS '关联任务ID';
COMMENT ON COLUMN alarm.session_id IS '关联会话ID';
COMMENT ON COLUMN alarm.rule_id IS '触发规则ID';
COMMENT ON COLUMN alarm.confidence IS '置信度（0-100）';
COMMENT ON COLUMN alarm.severity IS '严重程度（1-5）';
COMMENT ON COLUMN alarm.category IS '告警分类';
COMMENT ON COLUMN alarm.sub_category IS '告警子分类';
COMMENT ON COLUMN alarm.attack_stage IS '攻击阶段';
COMMENT ON COLUMN alarm.threat_intelligence IS '威胁情报信息';
COMMENT ON COLUMN alarm.remediation_advice IS '修复建议';
COMMENT ON COLUMN alarm.false_positive IS '是否误报';
COMMENT ON COLUMN alarm.analyst_notes IS '分析师备注';
COMMENT ON COLUMN alarm.escalated IS '是否已升级';
COMMENT ON COLUMN alarm.escalation_time IS '升级时间';
COMMENT ON COLUMN alarm.resolved_time IS '解决时间';
COMMENT ON COLUMN alarm.resolved_by IS '解决人';
COMMENT ON COLUMN alarm.resolution_notes IS '解决备注';

-- 告警来源表（对应AlarmSource实体）
DROP TABLE IF EXISTS alarm_source CASCADE;

CREATE TABLE alarm_source (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    source_type VARCHAR(50),
    source_name VARCHAR(100),
    source_ip VARCHAR(45),
    source_port INTEGER,
    source_mac VARCHAR(17),
    source_hostname VARCHAR(255),
    source_domain VARCHAR(255),
    geolocation VARCHAR(100),
    organization VARCHAR(255),
    reputation_score INTEGER,
    first_seen TIMESTAMP,
    last_seen TIMESTAMP,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (alarm_id) REFERENCES alarm(id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_source IS '告警来源表';
COMMENT ON COLUMN alarm_source.id IS '来源ID';
COMMENT ON COLUMN alarm_source.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_source.source_type IS '来源类型';
COMMENT ON COLUMN alarm_source.source_name IS '来源名称';
COMMENT ON COLUMN alarm_source.source_ip IS '来源IP';
COMMENT ON COLUMN alarm_source.source_port IS '来源端口';
COMMENT ON COLUMN alarm_source.source_mac IS '来源MAC地址';
COMMENT ON COLUMN alarm_source.source_hostname IS '来源主机名';
COMMENT ON COLUMN alarm_source.source_domain IS '来源域名';
COMMENT ON COLUMN alarm_source.geolocation IS '地理位置';
COMMENT ON COLUMN alarm_source.organization IS '所属组织';
COMMENT ON COLUMN alarm_source.reputation_score IS '信誉评分';
COMMENT ON COLUMN alarm_source.first_seen IS '首次发现时间';
COMMENT ON COLUMN alarm_source.last_seen IS '最后发现时间';

-- 告警攻击者表（对应AlarmAttacker实体）
DROP TABLE IF EXISTS alarm_attacker CASCADE;

CREATE TABLE alarm_attacker (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    attacker_ip VARCHAR(45),
    attacker_port INTEGER,
    attacker_mac VARCHAR(17),
    attacker_hostname VARCHAR(255),
    attacker_type VARCHAR(50),
    attack_method VARCHAR(100),
    tools_used TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (alarm_id) REFERENCES alarm(id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_attacker IS '告警攻击者表';
COMMENT ON COLUMN alarm_attacker.id IS '攻击者ID';
COMMENT ON COLUMN alarm_attacker.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_attacker.attacker_ip IS '攻击者IP';
COMMENT ON COLUMN alarm_attacker.attacker_port IS '攻击者端口';
COMMENT ON COLUMN alarm_attacker.attacker_mac IS '攻击者MAC地址';
COMMENT ON COLUMN alarm_attacker.attacker_hostname IS '攻击者主机名';
COMMENT ON COLUMN alarm_attacker.attacker_type IS '攻击者类型';
COMMENT ON COLUMN alarm_attacker.attack_method IS '攻击方法';
COMMENT ON COLUMN alarm_attacker.tools_used IS '使用的工具';

-- 告警受害者表（对应AlarmVictim实体）
DROP TABLE IF EXISTS alarm_victim CASCADE;

CREATE TABLE alarm_victim (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    victim_ip VARCHAR(45),
    victim_port INTEGER,
    victim_mac VARCHAR(17),
    victim_hostname VARCHAR(255),
    victim_type VARCHAR(50),
    asset_value VARCHAR(20),
    business_impact VARCHAR(100),
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (alarm_id) REFERENCES alarm(id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_victim IS '告警受害者表';
COMMENT ON COLUMN alarm_victim.id IS '受害者ID';
COMMENT ON COLUMN alarm_victim.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_victim.victim_ip IS '受害者IP';
COMMENT ON COLUMN alarm_victim.victim_port IS '受害者端口';
COMMENT ON COLUMN alarm_victim.victim_mac IS '受害者MAC地址';
COMMENT ON COLUMN alarm_victim.victim_hostname IS '受害者主机名';
COMMENT ON COLUMN alarm_victim.victim_type IS '受害者类型';
COMMENT ON COLUMN alarm_victim.asset_value IS '资产价值';
COMMENT ON COLUMN alarm_victim.business_impact IS '业务影响';

-- 告警目标表（对应AlarmTargets实体）
DROP TABLE IF EXISTS alarm_targets CASCADE;

CREATE TABLE alarm_targets (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    type VARCHAR(50),
    target VARCHAR(255),
    description TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (alarm_id) REFERENCES alarm(id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_targets IS '告警目标表';
COMMENT ON COLUMN alarm_targets.id IS '目标ID';
COMMENT ON COLUMN alarm_targets.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_targets.type IS '目标类型';
COMMENT ON COLUMN alarm_targets.target IS '目标值';
COMMENT ON COLUMN alarm_targets.description IS '目标描述';

-- 告警原因表（对应AlarmReason实体）
DROP TABLE IF EXISTS alarm_reason CASCADE;

CREATE TABLE alarm_reason (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    key VARCHAR(100),
    actual_value TEXT,
    expected_value TEXT,
    threshold_value TEXT,
    rule_condition TEXT,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (alarm_id) REFERENCES alarm(id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_reason IS '告警原因表';
COMMENT ON COLUMN alarm_reason.id IS '原因ID';
COMMENT ON COLUMN alarm_reason.alarm_id IS '告警主表ID';
COMMENT ON COLUMN alarm_reason.key IS '原因键';
COMMENT ON COLUMN alarm_reason.actual_value IS '实际值';
COMMENT ON COLUMN alarm_reason.expected_value IS '期望值';
COMMENT ON COLUMN alarm_reason.threshold_value IS '阈值';
COMMENT ON COLUMN alarm_reason.rule_condition IS '规则条件';

-- ========================================
-- 告警配置和管理表
-- ========================================

-- 告警类型配置表
DROP TABLE IF EXISTS alarm_type_config CASCADE;

CREATE TABLE alarm_type_config (
    id SERIAL PRIMARY KEY,
    alarm_type VARCHAR(100) NOT NULL UNIQUE,
    display_name VARCHAR(200),
    description TEXT,
    default_level VARCHAR(20) DEFAULT 'MEDIUM',
    default_severity INTEGER DEFAULT 3,
    category VARCHAR(50),
    sub_category VARCHAR(50),
    enabled BOOLEAN DEFAULT TRUE,
    auto_resolve BOOLEAN DEFAULT FALSE,
    auto_resolve_timeout INTEGER DEFAULT 3600,
    escalation_rules JSONB,
    notification_rules JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE alarm_type_config IS '告警类型配置表';
COMMENT ON COLUMN alarm_type_config.alarm_type IS '告警类型标识';
COMMENT ON COLUMN alarm_type_config.display_name IS '显示名称';
COMMENT ON COLUMN alarm_type_config.description IS '类型描述';
COMMENT ON COLUMN alarm_type_config.default_level IS '默认告警级别';
COMMENT ON COLUMN alarm_type_config.default_severity IS '默认严重程度';
COMMENT ON COLUMN alarm_type_config.category IS '告警分类';
COMMENT ON COLUMN alarm_type_config.sub_category IS '告警子分类';
COMMENT ON COLUMN alarm_type_config.enabled IS '是否启用';
COMMENT ON COLUMN alarm_type_config.auto_resolve IS '是否自动解决';
COMMENT ON COLUMN alarm_type_config.auto_resolve_timeout IS '自动解决超时时间（秒）';
COMMENT ON COLUMN alarm_type_config.escalation_rules IS '升级规则（JSON格式）';
COMMENT ON COLUMN alarm_type_config.notification_rules IS '通知规则（JSON格式）';

-- 告警规则表
DROP TABLE IF EXISTS alarm_rules CASCADE;

CREATE TABLE alarm_rules (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    alarm_type VARCHAR(100) NOT NULL,
    conditions JSONB NOT NULL,
    actions JSONB NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    priority INTEGER DEFAULT 50,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_by VARCHAR(100)
);

COMMENT ON TABLE alarm_rules IS '告警规则表';
COMMENT ON COLUMN alarm_rules.rule_name IS '规则名称';
COMMENT ON COLUMN alarm_rules.rule_type IS '规则类型';
COMMENT ON COLUMN alarm_rules.alarm_type IS '关联的告警类型';
COMMENT ON COLUMN alarm_rules.conditions IS '触发条件（JSON格式）';
COMMENT ON COLUMN alarm_rules.actions IS '执行动作（JSON格式）';
COMMENT ON COLUMN alarm_rules.enabled IS '是否启用';
COMMENT ON COLUMN alarm_rules.priority IS '优先级';

-- 告警记录表
DROP TABLE IF EXISTS alarm_records CASCADE;

CREATE TABLE alarm_records (
    id SERIAL PRIMARY KEY,
    alarm_id VARCHAR(32) NOT NULL,
    record_type VARCHAR(50) NOT NULL,
    operator VARCHAR(100),
    operation VARCHAR(100),
    old_value TEXT,
    new_value TEXT,
    comments TEXT,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (alarm_id) REFERENCES alarm(id) ON DELETE CASCADE
);

COMMENT ON TABLE alarm_records IS '告警记录表';
COMMENT ON COLUMN alarm_records.alarm_id IS '告警ID';
COMMENT ON COLUMN alarm_records.record_type IS '记录类型：STATUS_CHANGE, ASSIGNMENT, COMMENT, ESCALATION';
COMMENT ON COLUMN alarm_records.operator IS '操作人';
COMMENT ON COLUMN alarm_records.operation IS '操作类型';
COMMENT ON COLUMN alarm_records.old_value IS '旧值';
COMMENT ON COLUMN alarm_records.new_value IS '新值';
COMMENT ON COLUMN alarm_records.comments IS '操作备注';
COMMENT ON COLUMN alarm_records.ip_address IS '操作IP地址';
COMMENT ON COLUMN alarm_records.user_agent IS '用户代理';

-- ========================================
-- 告警订阅和通知表
-- ========================================

-- 告警订阅表
DROP TABLE IF EXISTS alarm_subscription CASCADE;

CREATE TABLE alarm_subscription (
    id VARCHAR(32) PRIMARY KEY,
    user_id INTEGER NOT NULL,
    subscription_name VARCHAR(255) NOT NULL,
    alarm_types TEXT[], -- 订阅的告警类型数组
    alarm_levels TEXT[], -- 订阅的告警级别数组
    categories TEXT[], -- 订阅的分类数组
    severity_min INTEGER DEFAULT 1,
    severity_max INTEGER DEFAULT 5,
    notification_methods TEXT[], -- 通知方式：EMAIL, SMS, WEBHOOK, KAFKA
    notification_config JSONB, -- 通知配置
    filters JSONB, -- 过滤条件
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE alarm_subscription IS '告警订阅表';
COMMENT ON COLUMN alarm_subscription.user_id IS '用户ID';
COMMENT ON COLUMN alarm_subscription.subscription_name IS '订阅名称';
COMMENT ON COLUMN alarm_subscription.alarm_types IS '订阅的告警类型';
COMMENT ON COLUMN alarm_subscription.alarm_levels IS '订阅的告警级别';
COMMENT ON COLUMN alarm_subscription.categories IS '订阅的分类';
COMMENT ON COLUMN alarm_subscription.severity_min IS '最小严重程度';
COMMENT ON COLUMN alarm_subscription.severity_max IS '最大严重程度';
COMMENT ON COLUMN alarm_subscription.notification_methods IS '通知方式';
COMMENT ON COLUMN alarm_subscription.notification_config IS '通知配置';
COMMENT ON COLUMN alarm_subscription.filters IS '过滤条件';
COMMENT ON COLUMN alarm_subscription.enabled IS '是否启用';

-- 通知模板表
DROP TABLE IF EXISTS notification_template CASCADE;

CREATE TABLE notification_template (
    id VARCHAR(32) PRIMARY KEY,
    template_name VARCHAR(255) NOT NULL,
    template_type VARCHAR(50) NOT NULL, -- EMAIL, SMS, WEBHOOK, KAFKA
    is_default BOOLEAN DEFAULT FALSE,
    subject_template TEXT,
    content_template TEXT NOT NULL,
    template_variables JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE notification_template IS '通知模板表';
COMMENT ON COLUMN notification_template.template_name IS '模板名称';
COMMENT ON COLUMN notification_template.template_type IS '模板类型';
COMMENT ON COLUMN notification_template.is_default IS '是否默认模板';
COMMENT ON COLUMN notification_template.subject_template IS '主题模板';
COMMENT ON COLUMN notification_template.content_template IS '内容模板';
COMMENT ON COLUMN notification_template.template_variables IS '模板变量定义';

-- 通知配置表
DROP TABLE IF EXISTS notification_config CASCADE;

CREATE TABLE notification_config (
    id VARCHAR(32) PRIMARY KEY,
    config_name VARCHAR(255) NOT NULL,
    notification_type VARCHAR(50) NOT NULL, -- EMAIL, SMS, WEBHOOK, KAFKA
    config_data JSONB NOT NULL,
    template_id VARCHAR(32),
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER,

    FOREIGN KEY (template_id) REFERENCES notification_template(id)
);

COMMENT ON TABLE notification_config IS '通知配置表';
COMMENT ON COLUMN notification_config.config_name IS '配置名称';
COMMENT ON COLUMN notification_config.notification_type IS '通知类型';
COMMENT ON COLUMN notification_config.config_data IS '配置数据';
COMMENT ON COLUMN notification_config.template_id IS '关联的模板ID';
COMMENT ON COLUMN notification_config.enabled IS '是否启用';

-- 通知日志表
DROP TABLE IF EXISTS notification_log CASCADE;

CREATE TABLE notification_log (
    id VARCHAR(32) PRIMARY KEY,
    alarm_id VARCHAR(32),
    subscription_id VARCHAR(32),
    config_id VARCHAR(32),
    notification_type VARCHAR(50) NOT NULL,
    recipient VARCHAR(255) NOT NULL,
    subject VARCHAR(500),
    content TEXT,
    status VARCHAR(20) NOT NULL, -- PENDING, SENT, FAILED, RETRY
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    sent_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (alarm_id) REFERENCES alarm(id) ON DELETE SET NULL,
    FOREIGN KEY (subscription_id) REFERENCES alarm_subscription(id) ON DELETE SET NULL,
    FOREIGN KEY (config_id) REFERENCES notification_config(id) ON DELETE SET NULL
);

COMMENT ON TABLE notification_log IS '通知日志表';
COMMENT ON COLUMN notification_log.alarm_id IS '告警ID';
COMMENT ON COLUMN notification_log.subscription_id IS '订阅ID';
COMMENT ON COLUMN notification_log.config_id IS '通知配置ID';
COMMENT ON COLUMN notification_log.notification_type IS '通知类型';
COMMENT ON COLUMN notification_log.recipient IS '接收者';
COMMENT ON COLUMN notification_log.subject IS '通知主题';
COMMENT ON COLUMN notification_log.content IS '通知内容';
COMMENT ON COLUMN notification_log.status IS '发送状态';
COMMENT ON COLUMN notification_log.error_message IS '错误信息';
COMMENT ON COLUMN notification_log.retry_count IS '重试次数';
COMMENT ON COLUMN notification_log.max_retries IS '最大重试次数';
COMMENT ON COLUMN notification_log.sent_at IS '发送时间';

-- ========================================
-- 告警知识库管理表
-- ========================================

-- 告警知识表
DROP TABLE IF EXISTS alarm_knowledge CASCADE;

CREATE TABLE alarm_knowledge (
    id BIGSERIAL PRIMARY KEY,
    knowledge_id BIGINT NOT NULL,
    knowledge_name VARCHAR(255) NOT NULL,
    knowledge_desc TEXT,
    attack_type INTEGER,
    attack_type_name VARCHAR(100),
    alarm_name VARCHAR(200),
    threat_level VARCHAR(20),
    attack_stage VARCHAR(50),
    indicators TEXT[],
    mitigation_steps TEXT[],
    references TEXT[],
    tags TEXT[],
    is_active BOOLEAN NOT NULL DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE alarm_knowledge IS '告警知识表';
COMMENT ON COLUMN alarm_knowledge.knowledge_id IS '知识ID';
COMMENT ON COLUMN alarm_knowledge.knowledge_name IS '知识名称';
COMMENT ON COLUMN alarm_knowledge.knowledge_desc IS '知识描述';
COMMENT ON COLUMN alarm_knowledge.attack_type IS '攻击类型';
COMMENT ON COLUMN alarm_knowledge.attack_type_name IS '攻击类型名称';
COMMENT ON COLUMN alarm_knowledge.alarm_name IS '告警名称';
COMMENT ON COLUMN alarm_knowledge.threat_level IS '威胁等级';
COMMENT ON COLUMN alarm_knowledge.attack_stage IS '攻击阶段';
COMMENT ON COLUMN alarm_knowledge.indicators IS '威胁指标';
COMMENT ON COLUMN alarm_knowledge.mitigation_steps IS '缓解步骤';
COMMENT ON COLUMN alarm_knowledge.references IS '参考资料';
COMMENT ON COLUMN alarm_knowledge.tags IS '标签';
COMMENT ON COLUMN alarm_knowledge.is_active IS '是否激活';

-- 知识类型视图表
DROP TABLE IF EXISTS knowledge_type_vo CASCADE;

CREATE TABLE knowledge_type_vo (
    id BIGINT PRIMARY KEY,
    attack_type INTEGER,
    attack_type_name VARCHAR(100),
    alarm_name VARCHAR(200),
    description TEXT,
    threat_level VARCHAR(20),
    category VARCHAR(50),
    sub_category VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE knowledge_type_vo IS '知识类型视图表';
COMMENT ON COLUMN knowledge_type_vo.id IS '知识ID';
COMMENT ON COLUMN knowledge_type_vo.attack_type IS '攻击类型';
COMMENT ON COLUMN knowledge_type_vo.attack_type_name IS '攻击类型名称';
COMMENT ON COLUMN knowledge_type_vo.alarm_name IS '告警名称';
COMMENT ON COLUMN knowledge_type_vo.description IS '描述';
COMMENT ON COLUMN knowledge_type_vo.threat_level IS '威胁等级';
COMMENT ON COLUMN knowledge_type_vo.category IS '分类';
COMMENT ON COLUMN knowledge_type_vo.sub_category IS '子分类';

-- ========================================
-- 告警辅助和配置表
-- ========================================

-- 告警规则抑制表
DROP TABLE IF EXISTS alarm_suppression CASCADE;

CREATE TABLE alarm_suppression (
    id SERIAL PRIMARY KEY,
    suppression_name VARCHAR(255) NOT NULL,
    alarm_types TEXT[],
    conditions JSONB NOT NULL,
    suppression_duration INTEGER, -- 抑制时长（秒）
    max_occurrences INTEGER DEFAULT 1, -- 最大发生次数
    time_window INTEGER DEFAULT 3600, -- 时间窗口（秒）
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE alarm_suppression IS '告警抑制表';
COMMENT ON COLUMN alarm_suppression.suppression_name IS '抑制规则名称';
COMMENT ON COLUMN alarm_suppression.alarm_types IS '适用的告警类型';
COMMENT ON COLUMN alarm_suppression.conditions IS '抑制条件';
COMMENT ON COLUMN alarm_suppression.suppression_duration IS '抑制时长（秒）';
COMMENT ON COLUMN alarm_suppression.max_occurrences IS '时间窗口内最大发生次数';
COMMENT ON COLUMN alarm_suppression.time_window IS '时间窗口（秒）';
COMMENT ON COLUMN alarm_suppression.enabled IS '是否启用';

-- 告警白名单表
DROP TABLE IF EXISTS alarm_whitelist CASCADE;

CREATE TABLE alarm_whitelist (
    id SERIAL PRIMARY KEY,
    whitelist_name VARCHAR(255) NOT NULL,
    whitelist_type VARCHAR(50) NOT NULL, -- IP, DOMAIN, USER, RULE
    whitelist_value VARCHAR(500) NOT NULL,
    alarm_types TEXT[],
    reason TEXT,
    expiry_time TIMESTAMP,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER,
    updated_by INTEGER
);

COMMENT ON TABLE alarm_whitelist IS '告警白名单表';
COMMENT ON COLUMN alarm_whitelist.whitelist_name IS '白名单名称';
COMMENT ON COLUMN alarm_whitelist.whitelist_type IS '白名单类型';
COMMENT ON COLUMN alarm_whitelist.whitelist_value IS '白名单值';
COMMENT ON COLUMN alarm_whitelist.alarm_types IS '适用的告警类型';
COMMENT ON COLUMN alarm_whitelist.reason IS '加入白名单的原因';
COMMENT ON COLUMN alarm_whitelist.expiry_time IS '过期时间';
COMMENT ON COLUMN alarm_whitelist.enabled IS '是否启用';

-- 告警统计表
DROP TABLE IF EXISTS alarm_statistics CASCADE;

CREATE TABLE alarm_statistics (
    id SERIAL PRIMARY KEY,
    stat_date DATE NOT NULL,
    stat_hour INTEGER,
    alarm_type VARCHAR(100),
    alarm_level VARCHAR(20),
    category VARCHAR(50),
    total_count INTEGER DEFAULT 0,
    resolved_count INTEGER DEFAULT 0,
    false_positive_count INTEGER DEFAULT 0,
    escalated_count INTEGER DEFAULT 0,
    avg_resolution_time INTEGER, -- 平均解决时间（秒）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

    UNIQUE (stat_date, stat_hour, alarm_type, alarm_level, category)
);

COMMENT ON TABLE alarm_statistics IS '告警统计表';
COMMENT ON COLUMN alarm_statistics.stat_date IS '统计日期';
COMMENT ON COLUMN alarm_statistics.stat_hour IS '统计小时（0-23，NULL表示全天）';
COMMENT ON COLUMN alarm_statistics.alarm_type IS '告警类型';
COMMENT ON COLUMN alarm_statistics.alarm_level IS '告警级别';
COMMENT ON COLUMN alarm_statistics.category IS '告警分类';
COMMENT ON COLUMN alarm_statistics.total_count IS '总告警数';
COMMENT ON COLUMN alarm_statistics.resolved_count IS '已解决告警数';
COMMENT ON COLUMN alarm_statistics.false_positive_count IS '误报数';
COMMENT ON COLUMN alarm_statistics.escalated_count IS '升级告警数';
COMMENT ON COLUMN alarm_statistics.avg_resolution_time IS '平均解决时间（秒）';

-- ========================================
-- 创建索引
-- ========================================

-- 告警主表索引
CREATE INDEX idx_alarm_alarm_type ON alarm (alarm_type);
CREATE INDEX idx_alarm_alarm_level ON alarm (alarm_level);
CREATE INDEX idx_alarm_alarm_time ON alarm (alarm_time);
CREATE INDEX idx_alarm_src_ip ON alarm (src_ip);
CREATE INDEX idx_alarm_dst_ip ON alarm (dst_ip);
CREATE INDEX idx_alarm_status ON alarm (status);
CREATE INDEX idx_alarm_task_id ON alarm (task_id);
CREATE INDEX idx_alarm_session_id ON alarm (session_id);
CREATE INDEX idx_alarm_rule_id ON alarm (rule_id);
CREATE INDEX idx_alarm_category ON alarm (category);
CREATE INDEX idx_alarm_severity ON alarm (severity);
CREATE INDEX idx_alarm_create_time ON alarm (create_time);
CREATE INDEX idx_alarm_resolved_time ON alarm (resolved_time);

-- 告警关联表索引
CREATE INDEX idx_alarm_source_alarm_id ON alarm_source (alarm_id);
CREATE INDEX idx_alarm_source_source_ip ON alarm_source (source_ip);
CREATE INDEX idx_alarm_attacker_alarm_id ON alarm_attacker (alarm_id);
CREATE INDEX idx_alarm_attacker_attacker_ip ON alarm_attacker (attacker_ip);
CREATE INDEX idx_alarm_victim_alarm_id ON alarm_victim (alarm_id);
CREATE INDEX idx_alarm_victim_victim_ip ON alarm_victim (victim_ip);
CREATE INDEX idx_alarm_targets_alarm_id ON alarm_targets (alarm_id);
CREATE INDEX idx_alarm_targets_type ON alarm_targets (type);
CREATE INDEX idx_alarm_reason_alarm_id ON alarm_reason (alarm_id);
CREATE INDEX idx_alarm_reason_key ON alarm_reason (key);

-- 告警配置表索引
CREATE INDEX idx_alarm_type_config_alarm_type ON alarm_type_config (alarm_type);
CREATE INDEX idx_alarm_type_config_category ON alarm_type_config (category);
CREATE INDEX idx_alarm_type_config_enabled ON alarm_type_config (enabled);
CREATE INDEX idx_alarm_rules_alarm_type ON alarm_rules (alarm_type);
CREATE INDEX idx_alarm_rules_enabled ON alarm_rules (enabled);
CREATE INDEX idx_alarm_records_alarm_id ON alarm_records (alarm_id);
CREATE INDEX idx_alarm_records_record_type ON alarm_records (record_type);
CREATE INDEX idx_alarm_records_created_at ON alarm_records (created_at);

-- 告警订阅和通知表索引
CREATE INDEX idx_alarm_subscription_user_id ON alarm_subscription (user_id);
CREATE INDEX idx_alarm_subscription_enabled ON alarm_subscription (enabled);
CREATE INDEX idx_notification_template_template_type ON notification_template (template_type);
CREATE INDEX idx_notification_config_notification_type ON notification_config (notification_type);
CREATE INDEX idx_notification_config_enabled ON notification_config (enabled);
CREATE INDEX idx_notification_log_alarm_id ON notification_log (alarm_id);
CREATE INDEX idx_notification_log_status ON notification_log (status);
CREATE INDEX idx_notification_log_created_at ON notification_log (created_at);

-- 告警知识库表索引
CREATE INDEX idx_alarm_knowledge_knowledge_id ON alarm_knowledge (knowledge_id);
CREATE INDEX idx_alarm_knowledge_attack_type ON alarm_knowledge (attack_type);
CREATE INDEX idx_alarm_knowledge_is_active ON alarm_knowledge (is_active);
CREATE INDEX idx_knowledge_type_vo_attack_type ON knowledge_type_vo (attack_type);

-- 告警辅助表索引
CREATE INDEX idx_alarm_suppression_enabled ON alarm_suppression (enabled);
CREATE INDEX idx_alarm_whitelist_whitelist_type ON alarm_whitelist (whitelist_type);
CREATE INDEX idx_alarm_whitelist_enabled ON alarm_whitelist (enabled);
CREATE INDEX idx_alarm_whitelist_expiry_time ON alarm_whitelist (expiry_time);
CREATE INDEX idx_alarm_statistics_stat_date ON alarm_statistics (stat_date);
CREATE INDEX idx_alarm_statistics_alarm_type ON alarm_statistics (alarm_type);

-- ========================================
-- 创建更新时间触发器
-- ========================================

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为告警相关表创建更新时间触发器
CREATE TRIGGER update_alarm_updated_at 
    BEFORE UPDATE ON alarm 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_type_config_updated_at 
    BEFORE UPDATE ON alarm_type_config 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_rules_updated_at 
    BEFORE UPDATE ON alarm_rules 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_subscription_updated_at 
    BEFORE UPDATE ON alarm_subscription 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_template_updated_at 
    BEFORE UPDATE ON notification_template 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_config_updated_at 
    BEFORE UPDATE ON notification_config 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notification_log_updated_at 
    BEFORE UPDATE ON notification_log 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_knowledge_updated_at 
    BEFORE UPDATE ON alarm_knowledge 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_knowledge_type_vo_updated_at 
    BEFORE UPDATE ON knowledge_type_vo 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_suppression_updated_at 
    BEFORE UPDATE ON alarm_suppression 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_whitelist_updated_at 
    BEFORE UPDATE ON alarm_whitelist 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_alarm_statistics_updated_at 
    BEFORE UPDATE ON alarm_statistics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ========================================
-- 插入默认通知模板数据
-- ========================================

-- 插入默认通知模板
INSERT INTO notification_template (id, template_name, template_type, is_default, subject_template, content_template, template_variables) VALUES
('default_email_template', '默认邮件模板', 'EMAIL', TRUE,
 '【NTA安全告警】${alarmType} - ${alarmLevel}',
 '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>安全告警通知</title>
</head>
<body>
    <h2>安全告警通知</h2>
    <table border="1" cellpadding="5" cellspacing="0">
        <tr><td><strong>告警类型</strong></td><td>${alarmType}</td></tr>
        <tr><td><strong>告警级别</strong></td><td>${alarmLevel}</td></tr>
        <tr><td><strong>告警时间</strong></td><td>${alarmTime}</td></tr>
        <tr><td><strong>源IP</strong></td><td>${srcIp}</td></tr>
        <tr><td><strong>目标IP</strong></td><td>${dstIp}</td></tr>
        <tr><td><strong>告警描述</strong></td><td>${description}</td></tr>
    </table>
    <p>请及时处理该安全告警。</p>
</body>
</html>',
 '{"alarmType": "告警类型", "alarmLevel": "告警级别", "alarmTime": "告警时间", "srcIp": "源IP", "dstIp": "目标IP", "description": "告警描述"}'::jsonb),

('default_kafka_template', '默认Kafka模板', 'KAFKA', TRUE,
 '${alarmType}告警',
 '{
    "alarmId": "${alarmId}",
    "alarmType": "${alarmType}",
    "alarmLevel": "${alarmLevel}",
    "alarmTime": "${alarmTime}",
    "srcIp": "${srcIp}",
    "dstIp": "${dstIp}",
    "description": "${description}",
    "notificationTime": "${notificationTime}"
}',
 '{"alarmId": "告警ID", "alarmType": "告警类型", "alarmLevel": "告警级别", "alarmTime": "告警时间", "srcIp": "源IP", "dstIp": "目标IP", "description": "告警描述", "notificationTime": "通知时间"}'::jsonb);

-- ========================================
-- 兼容性告警表（来自alarm-service-old.sql）
-- ========================================

-- 告警基础表（兼容旧系统）
DROP TABLE IF EXISTS alarm_base CASCADE;

CREATE TABLE alarm_base (
    id VARCHAR(255) PRIMARY KEY,
    task_id BIGINT,
    time TIMESTAMP,
    alarm_name VARCHAR(255),
    targets JSONB,
    victim JSONB,
    attacker JSONB,
    attack_level INTEGER,
    alarm_status INTEGER,
    json JSONB,
    alarm_knowledge_id BIGINT,
    alarm_source VARCHAR(255),
    attack_chain_name TEXT[]
);

COMMENT ON TABLE alarm_base IS '告警基础表（兼容旧系统）';
COMMENT ON COLUMN alarm_base.id IS '告警ID';
COMMENT ON COLUMN alarm_base.task_id IS '任务ID';
COMMENT ON COLUMN alarm_base.time IS '告警时间';
COMMENT ON COLUMN alarm_base.alarm_name IS '告警名称';
COMMENT ON COLUMN alarm_base.targets IS '告警对象（JSON格式）';
COMMENT ON COLUMN alarm_base.victim IS '受害者信息（JSON格式）';
COMMENT ON COLUMN alarm_base.attacker IS '攻击者信息（JSON格式）';
COMMENT ON COLUMN alarm_base.attack_level IS '威胁权重';
COMMENT ON COLUMN alarm_base.alarm_status IS '处理状态';
COMMENT ON COLUMN alarm_base.json IS '告警具体数据（JSON格式）';
COMMENT ON COLUMN alarm_base.alarm_knowledge_id IS '告警知识ID';
COMMENT ON COLUMN alarm_base.alarm_source IS '告警来源';
COMMENT ON COLUMN alarm_base.attack_chain_name IS '攻击链名称';

-- 创建兼容性表索引
CREATE INDEX idx_alarm_base_task_id ON alarm_base (task_id);
CREATE INDEX idx_alarm_base_time ON alarm_base (time);
CREATE INDEX idx_alarm_base_alarm_name ON alarm_base (alarm_name);
CREATE INDEX idx_alarm_base_alarm_status ON alarm_base (alarm_status);
CREATE INDEX idx_alarm_base_alarm_knowledge_id ON alarm_base (alarm_knowledge_id);
CREATE INDEX idx_alarm_base_alarm_source ON alarm_base (alarm_source);