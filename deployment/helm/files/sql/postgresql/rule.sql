-- ========================================
-- NTA 3.0 规则管理模块数据库结构
-- ========================================
-- 创建时间: 2025-01-22
-- 描述: 规则管理模块相关的所有表结构定义
-- 数据库: PostgreSQL
-- ========================================

-- ========================================
-- 过滤规则管理表
-- ========================================

-- 过滤规则表
DROP TABLE IF EXISTS filter_rule CASCADE;

CREATE TABLE filter_rule (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    ip VARCHAR(255) NOT NULL,
    filter_json JSON NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    hash VARCHAR(255) NOT NULL,
    criteria filter_criteria_enum NOT NULL,
    active BOOLEAN NOT NULL DEFAULT true
);

COMMENT ON TABLE filter_rule IS '过滤规则表';
COMMENT ON COLUMN filter_rule.task_id IS '任务ID';
COMMENT ON COLUMN filter_rule.ip IS 'IP地址';
COMMENT ON COLUMN filter_rule.filter_json IS '过滤规则JSON字符串';
COMMENT ON COLUMN filter_rule.created_at IS '创建时间';
COMMENT ON COLUMN filter_rule.updated_at IS '更新时间';
COMMENT ON COLUMN filter_rule.hash IS '规则hash值，用于校验重复';
COMMENT ON COLUMN filter_rule.criteria IS '过滤条件，使用filter_criteria_enum枚举';
COMMENT ON COLUMN filter_rule.active IS '是否激活 true:正常 false:删除';

-- ========================================
-- 威胁检测算法配置表
-- ========================================

-- 威胁检测算法配置表
DROP TABLE IF EXISTS threat_detection_algorithm_configs CASCADE;

CREATE TABLE threat_detection_algorithm_configs (
    algorithm_id INTEGER PRIMARY KEY,
    algorithm_name VARCHAR(1024) NOT NULL,
    algorithm_type VARCHAR(512) NOT NULL,
    description VARCHAR(2048) NOT NULL,
    algorithm_hash VARCHAR(255),
    algorithm_version VARCHAR(255),
    algorithm_path VARCHAR(255),
    state INTEGER NOT NULL DEFAULT 1,
    retain_metadata BOOLEAN NOT NULL DEFAULT false,
    retain_pcap BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE threat_detection_algorithm_configs IS '威胁检测算法配置表';
COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_id IS '算法ID';
COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_name IS '算法名称';
COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_type IS '算法类型（协议识别、特征识别、行为识别、LSTM神经网络、随机森林等）';
COMMENT ON COLUMN threat_detection_algorithm_configs.description IS '算法描述';
COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_hash IS '算法文件哈希';
COMMENT ON COLUMN threat_detection_algorithm_configs.algorithm_path IS '算法文件路径';
COMMENT ON COLUMN threat_detection_algorithm_configs.state IS '算法状态（1=启用，0=禁用）';
COMMENT ON COLUMN threat_detection_algorithm_configs.retain_metadata IS '是否留存流量元数据';
COMMENT ON COLUMN threat_detection_algorithm_configs.retain_pcap IS '是否留存原始流量数据（PCAP文件）';

-- 威胁检测算法统计表
DROP TABLE IF EXISTS threat_detection_algorithm_statistics CASCADE;

CREATE TABLE threat_detection_algorithm_statistics (
    algorithm_id INTEGER PRIMARY KEY REFERENCES threat_detection_algorithm_configs (algorithm_id),
    total_sum_bytes BIGINT NOT NULL DEFAULT 0,
    last_hit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE threat_detection_algorithm_statistics IS '威胁检测算法统计表';
COMMENT ON COLUMN threat_detection_algorithm_statistics.algorithm_id IS '算法ID';
COMMENT ON COLUMN threat_detection_algorithm_statistics.total_sum_bytes IS '算法命中数据总量';
COMMENT ON COLUMN threat_detection_algorithm_statistics.last_hit_time IS '最新命中时间';

-- ========================================
-- 检测规则配置表
-- ========================================

-- 检测规则配置表
DROP TABLE IF EXISTS detection_rule_configs CASCADE;

CREATE TABLE detection_rule_configs (
    id BIGSERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    label_id INTEGER NOT NULL UNIQUE,
    threat_level threat_level_enum NOT NULL,
    rule_name TEXT NOT NULL,
    rule_desc TEXT NOT NULL,
    rule_enabled BOOLEAN NOT NULL DEFAULT true,
    rule_source rule_source_enum NOT NULL DEFAULT 'SYSTEM',
    capture_mode capture_mode_enum NOT NULL,
    rule_json JSON NOT NULL,
    rule_hash VARCHAR(255) NOT NULL,
    cyber_kill_chain cyber_kill_chain_enum DEFAULT 'OTHER',
    traffic_rate_limit_bps BIGINT NOT NULL DEFAULT 0,
    traffic_retention_limit_bytes BIGINT NOT NULL DEFAULT -1,
    retain_metadata BOOLEAN NOT NULL DEFAULT false,
    retain_pcap BOOLEAN NOT NULL DEFAULT false,
    lib_respond_enabled BOOLEAN NOT NULL DEFAULT false,
    lib_respond_lib VARCHAR(255) NOT NULL DEFAULT '',
    lib_respond_config VARCHAR(255) NOT NULL DEFAULT '',
    lib_respond_session_end BIGINT NOT NULL DEFAULT 0,
    lib_respond_pkt_num BIGINT NOT NULL DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE detection_rule_configs IS '检测规则配置表';
COMMENT ON COLUMN detection_rule_configs.label_id IS '标签ID（规则命中时打上的标签）';
COMMENT ON COLUMN detection_rule_configs.threat_level IS '威胁等级，使用threat_level_enum枚举';
COMMENT ON COLUMN detection_rule_configs.rule_name IS '规则名称（告警类型）';
COMMENT ON COLUMN detection_rule_configs.rule_desc IS '规则描述';
COMMENT ON COLUMN detection_rule_configs.rule_enabled IS '是否启用规则 true:生效 false:失效';
COMMENT ON COLUMN detection_rule_configs.rule_source IS '规则来源，使用rule_source_enum枚举';
COMMENT ON COLUMN detection_rule_configs.capture_mode IS '采集模式，使用capture_mode_enum枚举';
COMMENT ON COLUMN detection_rule_configs.rule_json IS '规则JSON配置';
COMMENT ON COLUMN detection_rule_configs.cyber_kill_chain IS 'Cyber Kill Chain阶段，使用cyber_kill_chain_enum枚举';
COMMENT ON COLUMN detection_rule_configs.traffic_rate_limit_bps IS '流量留存限速，单位：字节/秒';
COMMENT ON COLUMN detection_rule_configs.traffic_retention_limit_bytes IS '流量留存上限，单位：字节（负数表示不限）';
COMMENT ON COLUMN detection_rule_configs.retain_metadata IS '是否留存流量元数据';
COMMENT ON COLUMN detection_rule_configs.retain_pcap IS '是否留存原始流量数据（PCAP文件）';
COMMENT ON COLUMN detection_rule_configs.lib_respond_enabled IS '是否开启动态库响应';

-- 检测规则统计表
DROP TABLE IF EXISTS detection_rule_statistics CASCADE;

CREATE TABLE detection_rule_statistics (
    rule_id INTEGER PRIMARY KEY REFERENCES detection_rule_configs (rule_id),
    rule_size BIGINT NOT NULL DEFAULT 0,
    total_sum_bytes BIGINT NOT NULL DEFAULT 0,
    last_hit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE detection_rule_statistics IS '检测规则统计表';
COMMENT ON COLUMN detection_rule_statistics.rule_id IS '规则ID';
COMMENT ON COLUMN detection_rule_statistics.rule_size IS '规则数据量';
COMMENT ON COLUMN detection_rule_statistics.total_sum_bytes IS '命中数据总量';
COMMENT ON COLUMN detection_rule_statistics.last_hit_time IS '最新命中时间';

-- 检测规则库配置表
DROP TABLE IF EXISTS detection_rule_library_config CASCADE;

CREATE TABLE detection_rule_library_config (
    id BIGSERIAL PRIMARY KEY,
    lib_path TEXT NOT NULL,
    config_path TEXT NOT NULL
);

COMMENT ON TABLE detection_rule_library_config IS '检测规则库配置表';

-- ========================================
-- 规则服务相关表
-- ========================================

-- 规则定义表
DROP TABLE IF EXISTS rule_definitions CASCADE;

CREATE TABLE rule_definitions (
    id SERIAL PRIMARY KEY,
    rule_name VARCHAR(255) NOT NULL,
    rule_type VARCHAR(50) NOT NULL,
    rule_content TEXT NOT NULL,
    rule_format VARCHAR(20) NOT NULL DEFAULT 'JSON',
    priority INTEGER DEFAULT 0,
    enabled BOOLEAN NOT NULL DEFAULT true,
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100)
);

COMMENT ON TABLE rule_definitions IS '规则定义表';
COMMENT ON COLUMN rule_definitions.rule_format IS '规则格式: JSON, YAML, DROOLS';
COMMENT ON COLUMN rule_definitions.priority IS '优先级，数值越大优先级越高';

-- 规则执行记录表
DROP TABLE IF EXISTS rule_executions CASCADE;

CREATE TABLE rule_executions (
    id BIGSERIAL PRIMARY KEY,
    rule_id INTEGER REFERENCES rule_definitions (id),
    input_data JSONB,
    output_data JSONB,
    execution_time_ms INTEGER,
    status VARCHAR(20) NOT NULL,
    error_message TEXT,
    executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

COMMENT ON TABLE rule_executions IS '规则执行记录表';
COMMENT ON COLUMN rule_executions.rule_id IS '关联的规则ID';
COMMENT ON COLUMN rule_executions.input_data IS '输入数据（JSON格式）';
COMMENT ON COLUMN rule_executions.output_data IS '输出数据（JSON格式）';
COMMENT ON COLUMN rule_executions.execution_time_ms IS '执行时间（毫秒）';
COMMENT ON COLUMN rule_executions.status IS '执行状态: SUCCESS, FAILED, TIMEOUT';
COMMENT ON COLUMN rule_executions.error_message IS '错误信息';
COMMENT ON COLUMN rule_executions.executed_at IS '执行时间';

-- ========================================
-- 流量白名单管理表
-- ========================================

-- 流量白名单表
DROP TABLE IF EXISTS traffic_whitelist CASCADE;

CREATE TABLE traffic_whitelist (
    id SERIAL PRIMARY KEY,
    server_ip VARCHAR(50) NOT NULL DEFAULT '',
    port INTEGER NOT NULL DEFAULT -1,
    app_id INTEGER NOT NULL DEFAULT -1,
    rule_id INTEGER NOT NULL DEFAULT -1,
    rule_level INTEGER NOT NULL DEFAULT -1,
    rule_name TEXT NOT NULL DEFAULT '',
    rule_desc TEXT NOT NULL DEFAULT '',
    rule_state VARCHAR(255) NOT NULL DEFAULT '生效',
    rule_size BIGINT NOT NULL DEFAULT 0,
    total_sum_bytes BIGINT NOT NULL DEFAULT 0,
    last_size_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    capture_mode INTEGER NOT NULL DEFAULT 0,
    rule_json JSON NOT NULL DEFAULT '{}',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    rule_hash VARCHAR(255) NOT NULL DEFAULT '',
    cyber_kill_chain cyber_kill_chain_enum NOT NULL DEFAULT 'OTHER',
    task_id INTEGER NOT NULL DEFAULT 0
);

COMMENT ON TABLE traffic_whitelist IS '流量白名单表';
COMMENT ON COLUMN traffic_whitelist.server_ip IS '服务器IP';
COMMENT ON COLUMN traffic_whitelist.port IS '端口';
COMMENT ON COLUMN traffic_whitelist.app_id IS '应用id';
COMMENT ON COLUMN traffic_whitelist.cyber_kill_chain IS 'Cyber Kill Chain阶段，使用cyber_kill_chain_enum枚举';

-- 流量白名单日志表
DROP TABLE IF EXISTS traffic_whitelist_log CASCADE;

CREATE TABLE traffic_whitelist_log (
    id SERIAL PRIMARY KEY,
    last_filter VARCHAR(50) NOT NULL,
    last_total VARCHAR(50) NOT NULL,
    white_list_id INTEGER NOT NULL
);

COMMENT ON TABLE traffic_whitelist_log IS '流量白名单日志表';
COMMENT ON COLUMN traffic_whitelist_log.last_filter IS '上次过滤日志数量';
COMMENT ON COLUMN traffic_whitelist_log.last_total IS '上次全部日志数量';
COMMENT ON COLUMN traffic_whitelist_log.white_list_id IS '外键 white_list 的主键';

-- 流量白名单状态表
DROP TABLE IF EXISTS traffic_whitelist_state CASCADE;

CREATE TABLE traffic_whitelist_state (
    id SERIAL PRIMARY KEY,
    task_id INTEGER NOT NULL,
    state INTEGER NOT NULL,
    UNIQUE (task_id)
);

COMMENT ON TABLE traffic_whitelist_state IS '流量白名单状态表';
COMMENT ON COLUMN traffic_whitelist_state.task_id IS '任务ID';
COMMENT ON COLUMN traffic_whitelist_state.state IS '白名单状态：0-停用，1-启用';

-- ========================================
-- 创建索引
-- ========================================

-- 过滤规则表索引
CREATE INDEX idx_filter_rule_task_id ON filter_rule (task_id);
CREATE INDEX idx_filter_rule_ip ON filter_rule (ip);
CREATE INDEX idx_filter_rule_criteria ON filter_rule (criteria);
CREATE INDEX idx_filter_rule_active ON filter_rule (active);
CREATE INDEX idx_filter_rule_hash ON filter_rule (hash);

-- 威胁检测算法配置表索引
CREATE INDEX idx_threat_detection_algorithm_configs_state ON threat_detection_algorithm_configs (state);
CREATE INDEX idx_threat_detection_algorithm_configs_algorithm_type ON threat_detection_algorithm_configs (algorithm_type);
CREATE INDEX idx_threat_detection_algorithm_configs_algorithm_name ON threat_detection_algorithm_configs (algorithm_name);

-- 威胁检测算法统计表索引
CREATE INDEX idx_threat_detection_algorithm_statistics_last_size_time ON threat_detection_algorithm_statistics (last_hit_time);
CREATE INDEX idx_threat_detection_algorithm_statistics_total_sum_bytes ON threat_detection_algorithm_statistics (total_sum_bytes);

-- 检测规则配置表索引
CREATE INDEX idx_detection_rule_configs_task_id ON detection_rule_configs (task_id);
CREATE INDEX idx_detection_rule_configs_rule_state ON detection_rule_configs (rule_enabled);
CREATE INDEX idx_detection_rule_configs_attack_stage ON detection_rule_configs (cyber_kill_chain);
CREATE INDEX idx_detection_rule_configs_rule_level ON detection_rule_configs (threat_level);
CREATE INDEX idx_detection_rule_configs_label_id ON detection_rule_configs (label_id);
CREATE INDEX idx_detection_rule_configs_rule_source ON detection_rule_configs (rule_source);

-- 检测规则统计表索引
CREATE INDEX idx_detection_rule_statistics_last_hit_time ON detection_rule_statistics (last_hit_time);
CREATE INDEX idx_detection_rule_statistics_total_sum_bytes ON detection_rule_statistics (total_sum_bytes);

-- 规则定义表索引
CREATE INDEX idx_rule_definitions_rule_type ON rule_definitions (rule_type);
CREATE INDEX idx_rule_definitions_enabled ON rule_definitions (enabled);
CREATE INDEX idx_rule_definitions_priority ON rule_definitions (priority);
CREATE INDEX idx_rule_definitions_created_by ON rule_definitions (created_by);

-- 规则执行记录表索引
CREATE INDEX idx_rule_executions_rule_id ON rule_executions (rule_id);
CREATE INDEX idx_rule_executions_status ON rule_executions (status);
CREATE INDEX idx_rule_executions_executed_at ON rule_executions (executed_at);

-- 流量白名单表索引
CREATE INDEX idx_traffic_whitelist_server_ip ON traffic_whitelist (server_ip);
CREATE INDEX idx_traffic_whitelist_task_id ON traffic_whitelist (task_id);
CREATE INDEX idx_traffic_whitelist_rule_state ON traffic_whitelist (rule_state);
CREATE INDEX idx_traffic_whitelist_cyber_kill_chain ON traffic_whitelist (cyber_kill_chain);
CREATE INDEX idx_traffic_whitelist_created_at ON traffic_whitelist (created_at);

-- 流量白名单日志表索引
CREATE INDEX idx_traffic_whitelist_log_white_list_id ON traffic_whitelist_log (white_list_id);

-- 流量白名单状态表索引
CREATE INDEX idx_traffic_whitelist_state_task_id ON traffic_whitelist_state (task_id);
CREATE INDEX idx_traffic_whitelist_state_state ON traffic_whitelist_state (state);

-- ========================================
-- 创建更新时间触发器
-- ========================================

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为规则管理表创建更新时间触发器
CREATE TRIGGER update_filter_rule_updated_at 
    BEFORE UPDATE ON filter_rule 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_threat_detection_algorithm_configs_updated_at 
    BEFORE UPDATE ON threat_detection_algorithm_configs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_threat_detection_algorithm_statistics_updated_at 
    BEFORE UPDATE ON threat_detection_algorithm_statistics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_detection_rule_configs_updated_at 
    BEFORE UPDATE ON detection_rule_configs 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_detection_rule_statistics_updated_at 
    BEFORE UPDATE ON detection_rule_statistics 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_rule_definitions_updated_at
    BEFORE UPDATE ON rule_definitions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 为流量白名单表创建更新时间触发器
CREATE TRIGGER update_traffic_whitelist_updated_at
    BEFORE UPDATE ON traffic_whitelist
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
